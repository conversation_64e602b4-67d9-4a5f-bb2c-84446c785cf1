import { EnvVariable, ValidationResult } from '../types';
/**
 * Example-based validator that compares .env against .env.example
 */
export declare class ExampleValidator {
    private exampleVariables;
    private ignorePatterns;
    constructor(exampleVariables: EnvVariable[], ignorePatterns?: string[]);
    /**
     * Validate environment variables against example file
     */
    validate(variables: EnvVariable[]): ValidationResult;
    /**
     * Validate missing variables
     */
    private validateMissingVariables;
    /**
     * Validate unused variables
     */
    private validateUnusedVariables;
    /**
     * Validate empty values
     */
    private validateEmptyValues;
    /**
     * Validate value formats against examples
     */
    private validateValueFormats;
    /**
     * Detect format mismatches between actual and example values
     */
    private detectFormatMismatch;
    /**
     * Get format information for a variable
     */
    private getFormatInfo;
    /**
     * Generate suggestion from example variable
     */
    private generateSuggestionFromExample;
    /**
     * Check if a value is a placeholder
     */
    private isPlaceholderValue;
    /**
     * Check if value looks like a URL
     */
    private looksLikeUrl;
    /**
     * Check if value looks like a number
     */
    private looksLikeNumber;
    /**
     * Check if value looks like a boolean
     */
    private looksLikeBoolean;
    /**
     * Check if value looks like an email
     */
    private looksLikeEmail;
    /**
     * Check if variable should be ignored
     */
    private shouldIgnoreVariable;
    /**
     * Create validation summary
     */
    private createSummary;
}
//# sourceMappingURL=example-validator.d.ts.map