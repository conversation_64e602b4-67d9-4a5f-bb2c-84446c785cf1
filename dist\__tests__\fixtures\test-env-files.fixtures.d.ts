/**
 * Test fixtures for .env files
 */
export declare const validEnvFile = "# Application Configuration\nNODE_ENV=development\nPORT=3000\nDATABASE_URL=postgresql://user:password@localhost:5432/testdb\n\n# Authentication\nJWT_SECRET=super-secret-jwt-key-for-testing\nAPI_KEY=test-api-key-12345\n\n# Feature Flags\nDEBUG=true\nENABLE_FEATURE_X=false\n\n# Empty value (should be allowed for some variables)\nOPTIONAL_VAR=\n";
export declare const invalidEnvFile = "# Invalid .env file with syntax errors\nNODE_ENV=development\nINVALID LINE WITHOUT EQUALS\nPORT=3000\n=MISSING_KEY\nANOTHER_INVALID_LINE\nDATABASE_URL=postgresql://user:password@localhost:5432/testdb\n";
export declare const envFileWithSecurityIssues = "# Environment file with security issues\nNODE_ENV=production\nPORT=3000\n\n# Weak secrets\nJWT_SECRET=secret\nPASSWORD=admin\nAPI_KEY=12345\n\n# Hardcoded credentials\nDATABASE_URL=postgresql://admin:password@localhost:5432/proddb\nADMIN_PASSWORD=admin123\n\n# Potential PII\nUSER_EMAIL=<EMAIL>\nCREDIT_CARD=4111-1111-1111-1111\n";
export declare const exampleEnvFile = "# Example environment configuration\nNODE_ENV=development\nPORT=3000\nDATABASE_URL=postgresql://user:password@localhost:5432/dbname\n\n# Authentication\nJWT_SECRET=your-super-secret-jwt-key\nAPI_KEY=your-api-key-here\n\n# Feature Flags\nDEBUG=false\nENABLE_FEATURE_X=false\n\n# Optional variables\nOPTIONAL_VAR=\nREDIS_URL=redis://localhost:6379\n";
export declare const envFileWithUnusedVars = "# Environment file with unused variables\nNODE_ENV=development\nPORT=3000\nDATABASE_URL=postgresql://user:password@localhost:5432/testdb\n\n# These variables are not in the example\nUNUSED_VAR_1=value1\nUNUSED_VAR_2=value2\nLEGACY_SETTING=old_value\n";
export declare const envFileWithMissingVars = "# Environment file missing required variables\nNODE_ENV=development\n# PORT is missing\n# DATABASE_URL is missing\n\n# Only some variables present\nJWT_SECRET=test-secret\nDEBUG=true\n";
export declare const envFileWithQuotes = "# Environment file with various quote styles\nSINGLE_QUOTED='single quoted value'\nDOUBLE_QUOTED=\"double quoted value\"\nUNQUOTED=unquoted value\nQUOTED_WITH_SPACES=\"value with spaces\"\nQUOTED_WITH_SPECIAL=\"value with $pecial ch@racters\"\nEMPTY_QUOTED=\"\"\nEMPTY_SINGLE_QUOTED=''\n\n# Quotes with comments\nQUOTED_WITH_COMMENT=\"value\" # This is a comment\nSINGLE_WITH_COMMENT='value' # Another comment\n";
export declare const envFileWithComments = "# Main configuration\nNODE_ENV=development # Environment setting\nPORT=3000 # Server port\n\n# Database configuration\nDATABASE_URL=postgresql://user:password@localhost:5432/testdb # Main database\n\n# Authentication settings\nJWT_SECRET=test-secret # JWT signing key\nAPI_KEY=test-key # External API key\n\n# Feature flags\nDEBUG=true # Enable debug mode\nENABLE_FEATURE_X=false # Experimental feature\n";
export declare const testSchema: {
    variables: {
        NODE_ENV: {
            required: boolean;
            type: "string";
            description: string;
            example: string;
            allowEmpty: boolean;
        };
        PORT: {
            required: boolean;
            type: "number";
            description: string;
            example: string;
            allowEmpty: boolean;
        };
        DATABASE_URL: {
            required: boolean;
            type: "url";
            description: string;
            example: string;
            allowEmpty: boolean;
        };
        JWT_SECRET: {
            required: boolean;
            type: "string";
            description: string;
            example: string;
            allowEmpty: boolean;
        };
        API_KEY: {
            required: boolean;
            type: "string";
            description: string;
            example: string;
            allowEmpty: boolean;
        };
        DEBUG: {
            required: boolean;
            type: "boolean";
            description: string;
            example: string;
            allowEmpty: boolean;
        };
        OPTIONAL_VAR: {
            required: boolean;
            type: "string";
            description: string;
            example: string;
            allowEmpty: boolean;
        };
    };
    ignorePatterns: string[];
};
export declare const testConfig: {
    envFile: string;
    exampleFile: string;
    outputFormat: "text";
    strict: boolean;
    verbose: boolean;
    exitOnError: boolean;
    ignorePatterns: string[];
    customRules: {
        name: string;
        description: string;
        severity: "warning";
        pattern: RegExp;
    }[];
    securityRules: {
        name: string;
        description: string;
        pattern: RegExp;
        severity: "error";
        suggestion: string;
    }[];
};
//# sourceMappingURL=test-env-files.fixtures.d.ts.map