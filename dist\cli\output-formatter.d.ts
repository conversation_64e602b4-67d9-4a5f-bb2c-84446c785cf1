import { ValidationResult } from '../types';
/**
 * Output formatter for validation results
 */
export declare class OutputFormatter {
    private outputFormat;
    constructor(format?: 'text' | 'json' | 'junit');
    /**
     * Format validation result
     */
    format(result: ValidationResult): string;
    /**
     * Format as human-readable text
     */
    private formatText;
    /**
     * Format error message
     */
    private formatError;
    /**
     * Format warning message
     */
    private formatWarning;
    /**
     * Format info message
     */
    private formatInfo;
    /**
     * Format summary
     */
    private formatSummary;
    /**
     * Format as JSON
     */
    private formatJson;
    /**
     * Format as JUnit XML
     */
    private formatJUnit;
    /**
     * Format JUnit test case
     */
    private formatJUnitTestCase;
    /**
     * Escape XML special characters
     */
    private escapeXml;
}
//# sourceMappingURL=output-formatter.d.ts.map