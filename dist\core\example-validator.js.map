{"version": 3, "file": "example-validator.js", "sourceRoot": "", "sources": ["../../src/core/example-validator.ts"], "names": [], "mappings": ";;;AASA;;GAEG;AACH,MAAa,gBAAgB;IAI3B,YAAY,gBAA+B,EAAE,iBAA2B,EAAE;QACxE,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,SAAwB;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,MAAM,QAAQ,GAAwB,EAAE,CAAC;QACzC,MAAM,IAAI,GAAqB,EAAE,CAAC;QAElC,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAEvE,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;QAC/C,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;QAElD,kEAAkE;QAClE,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;QAE5E,iEAAiE;QACjE,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAE1E,kDAAkD;QAClD,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAE1D,8CAA8C;QAC9C,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEjE,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEhF,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;YACN,QAAQ;YACR,IAAI;YACJ,OAAO;SACR,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,wBAAwB,CAC9B,WAAqB,EACrB,WAAqC,EACrC,UAAoC,EACpC,MAAyB;QAEzB,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7D,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;gBACxC,MAAM,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;gBAElE,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,kBAAkB;oBACxB,QAAQ,EAAE,GAAG;oBACb,OAAO,EAAE,aAAa,GAAG,oDAAoD;oBAC7E,UAAU;oBACV,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAC7B,OAAiB,EACjB,WAAqB,EACrB,WAAqC,EACrC,QAA6B;QAE7B,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;QAE3C,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/D,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;gBACvC,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,iBAAiB;oBACvB,QAAQ,EAAE,GAAG;oBACb,OAAO,EAAE,aAAa,GAAG,kCAAkC;oBAC3D,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,8DAA8D;oBAC1E,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,SAAwB,EACxB,UAAoC,EACpC,QAA6B;QAE7B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAChD,IACE,UAAU;gBACV,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE;gBAC5B,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE;gBAC9B,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,EAC1C,CAAC;gBACD,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,iBAAiB;oBACvB,QAAQ,EAAE,QAAQ,CAAC,GAAG;oBACtB,OAAO,EAAE,aAAa,QAAQ,CAAC,GAAG,uCAAuC;oBACzE,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,sCAAsC,UAAU,CAAC,KAAK,EAAE;oBACpE,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAC1B,SAAwB,EACxB,UAAoC,EACpC,QAA6B,EAC7B,IAAsB;QAEtB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE;gBAAE,SAAS;YAE1D,mDAAmD;YACnD,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACvE,IAAI,cAAc,EAAE,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,cAAc;oBACpB,QAAQ,EAAE,QAAQ,CAAC,GAAG;oBACtB,OAAO,EAAE,cAAc,CAAC,OAAO;oBAC/B,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,cAAc,CAAC,UAAU;oBACrC,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;YAED,8CAA8C;YAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC5D,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC,IAAI,CAAC;oBACR,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,QAAQ,CAAC,GAAG;oBACtB,OAAO,EAAE,UAAU;oBACnB,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,QAAQ,EAAE,MAAM;iBACjB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAC1B,QAAqB,EACrB,UAAuB;QAEvB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC7B,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC;QAEjC,mCAAmC;QACnC,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAElD,mBAAmB;QACnB,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5D,OAAO;gBACL,OAAO,EAAE,aAAa,QAAQ,CAAC,GAAG,oCAAoC;gBACtE,UAAU,EAAE,6BAA6B,OAAO,EAAE;aACnD,CAAC;QACJ,CAAC;QAED,sBAAsB;QACtB,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAClE,OAAO;gBACL,OAAO,EAAE,aAAa,QAAQ,CAAC,GAAG,uCAAuC;gBACzE,UAAU,EAAE,gCAAgC,OAAO,EAAE;aACtD,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;YACpE,OAAO;gBACL,OAAO,EAAE,aAAa,QAAQ,CAAC,GAAG,wCAAwC;gBAC1E,UAAU,EAAE,iCAAiC,OAAO,EAAE;aACvD,CAAC;QACJ,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;YAChE,OAAO;gBACL,OAAO,EAAE,aAAa,QAAQ,CAAC,GAAG,uCAAuC;gBACzE,UAAU,EAAE,+BAA+B,OAAO,EAAE;aACrD,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,QAAqB,EAAE,UAAuB;QAClE,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC;QAEjC,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,OAAO,aAAa,QAAQ,CAAC,GAAG,kCAAkC,OAAO,EAAE,CAAC;QAC9E,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,6BAA6B,CAAC,UAAuB;QAC3D,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3E,OAAO,GAAG,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAa;QACtC,MAAM,mBAAmB,GAAG;YAC1B,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,eAAe;YACf,SAAS;SACV,CAAC;QAEF,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,KAAa;QAChC,IAAI,CAAC;YACH,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAa;QACnC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAa;QACpC,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAa;QAClC,OAAO,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,GAAW;QACtC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACxC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;YAClC,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa,CACnB,SAAwB,EACxB,MAAyB,EACzB,QAA6B,EAC7B,cAAsB;QAEtB,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,kBAAkB,CAAC,CAAC,MAAM,CAAC;QAClF,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,CAAC,MAAM,CAAC;QAElF,OAAO;YACL,cAAc,EAAE,SAAS,CAAC,MAAM;YAChC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM;YAC/C,gBAAgB;YAChB,eAAe;YACf,cAAc,EAAE,CAAC,EAAE,0CAA0C;YAC7D,cAAc;SACf,CAAC;IACJ,CAAC;CACF;AA/TD,4CA+TC"}