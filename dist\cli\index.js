#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvGuardCLI = void 0;
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const validator_1 = require("../core/validator");
const output_formatter_1 = require("./output-formatter");
const loader_1 = require("../config/loader");
const package_json_1 = require("../../package.json");
/**
 * CLI interface for EnvGuard
 */
class EnvGuardCLI {
    constructor() {
        this.program = new commander_1.Command();
        this.setupCommands();
    }
    /**
     * Setup CLI commands and options
     */
    setupCommands() {
        this.program
            .name('envguard')
            .description('Environment File Validator with Security Analysis')
            .version(package_json_1.version);
        // Main validate command
        this.program
            .command('validate')
            .description('Validate environment file against schema or example')
            .option('-e, --env-file <file>', 'Environment file to validate', '.env')
            .option('-s, --schema-file <file>', 'Schema file for validation')
            .option('-x, --example-file <file>', 'Example file for validation', '.env.example')
            .option('-c, --config-file <file>', 'Configuration file')
            .option('-m, --mode <mode>', 'Validation mode: schema, example, or both', 'example')
            .option('-f, --format <format>', 'Output format: text, json, or junit', 'text')
            .option('--strict', 'Enable strict mode (treat warnings as errors)', false)
            .option('--verbose', 'Enable verbose output', false)
            .option('--exit-on-error', 'Exit with non-zero code on validation errors', true)
            .option('--ignore-patterns <patterns>', 'Comma-separated ignore patterns', '')
            .action(async (options) => {
            await this.handleValidateCommand(options);
        });
        // Init command to create configuration files
        this.program
            .command('init')
            .description('Initialize EnvGuard configuration files')
            .option('--schema', 'Create schema file template', false)
            .option('--config', 'Create config file template', false)
            .option('--example', 'Create .env.example template', false)
            .action(async (options) => {
            await this.handleInitCommand(options);
        });
        // Check command for quick validation
        this.program
            .command('check')
            .description('Quick validation check (exit code only)')
            .option('-e, --env-file <file>', 'Environment file to validate', '.env')
            .option('-x, --example-file <file>', 'Example file for validation', '.env.example')
            .option('-c, --config-file <file>', 'Configuration file')
            .action(async (options) => {
            await this.handleCheckCommand(options);
        });
    }
    /**
     * Handle validate command
     */
    async handleValidateCommand(options) {
        try {
            const cliOptions = this.parseOptions(options);
            const config = await this.loadConfig(cliOptions);
            this.displayBanner();
            if (cliOptions.verbose) {
                console.log(chalk_1.default.gray(`Validating: ${config.envFile}`));
                console.log(chalk_1.default.gray(`Mode: ${cliOptions.mode}`));
                console.log(chalk_1.default.gray(`Format: ${cliOptions.outputFormat}\n`));
            }
            const validator = new validator_1.EnvGuardValidator(config);
            const result = await validator.validate();
            const formatter = new output_formatter_1.OutputFormatter(cliOptions.outputFormat);
            const output = formatter.format(result);
            console.log(output);
            // Exit with appropriate code
            if (cliOptions.exitOnError && !result.isValid) {
                process.exit(1);
            }
            else if (cliOptions.strict && result.warnings.length > 0) {
                process.exit(1);
            }
        }
        catch (error) {
            console.error(chalk_1.default.red(`Error: ${error.message}`));
            process.exit(1);
        }
    }
    /**
     * Handle init command
     */
    async handleInitCommand(options) {
        try {
            this.displayBanner();
            const { createSchemaTemplate, createConfigTemplate, createExampleTemplate } = await Promise.resolve().then(() => __importStar(require('../utils/templates')));
            if (options.schema) {
                await createSchemaTemplate();
                console.log(chalk_1.default.green('✅ Created schema template: envguard.schema.json'));
            }
            if (options.config) {
                await createConfigTemplate();
                console.log(chalk_1.default.green('✅ Created config template: envguard.config.json'));
            }
            if (options.example) {
                await createExampleTemplate();
                console.log(chalk_1.default.green('✅ Created example template: .env.example'));
            }
            if (!options.schema && !options.config && !options.example) {
                // Create all templates by default
                await createSchemaTemplate();
                await createConfigTemplate();
                await createExampleTemplate();
                console.log(chalk_1.default.green('✅ Created all template files'));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red(`Error: ${error.message}`));
            process.exit(1);
        }
    }
    /**
     * Handle check command
     */
    async handleCheckCommand(options) {
        try {
            const cliOptions = this.parseOptions(options);
            const config = await this.loadConfig(cliOptions);
            const validator = new validator_1.EnvGuardValidator(config);
            const result = await validator.validate();
            // Silent mode - only exit codes
            if (!result.isValid) {
                process.exit(1);
            }
        }
        catch (error) {
            process.exit(1);
        }
    }
    /**
     * Display awesome ASCII art banner
     */
    displayBanner() {
        const banner = `
${chalk_1.default.cyan('╔══════════════════════════════════════════════════════════════╗')}
${chalk_1.default.cyan('║')}                                                              ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}  ${chalk_1.default.bold.green('███████╗███╗   ██╗██╗   ██╗ ██████╗ ██╗   ██╗ █████╗ ██████╗ ██████╗')}  ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}  ${chalk_1.default.bold.green('██╔════╝████╗  ██║██║   ██║██╔════╝ ██║   ██║██╔══██╗██╔══██╗██╔══██╗')} ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}  ${chalk_1.default.bold.green('█████╗  ██╔██╗ ██║██║   ██║██║  ███╗██║   ██║███████║██████╔╝██║  ██║')} ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}  ${chalk_1.default.bold.green('██╔══╝  ██║╚██╗██║╚██╗ ██╔╝██║   ██║██║   ██║██╔══██║██╔══██╗██║  ██║')} ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}  ${chalk_1.default.bold.green('███████╗██║ ╚████║ ╚████╔╝ ╚██████╔╝╚██████╔╝██║  ██║██║  ██║██████╔╝')} ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}  ${chalk_1.default.bold.green('╚══════╝╚═╝  ╚═══╝  ╚═══╝   ╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝')}  ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}                                                              ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}           ${chalk_1.default.bold.yellow('🛡️  Environment File Validator & Security Analyzer')}           ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}                     ${chalk_1.default.gray('Secure your environment variables')}                     ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('║')}                                                              ${chalk_1.default.cyan('║')}
${chalk_1.default.cyan('╚══════════════════════════════════════════════════════════════╝')}
`;
        console.log(banner);
        console.log(); // Add spacing after banner
    }
    /**
     * Parse CLI options
     */
    parseOptions(options) {
        return {
            envFile: options.envFile || '.env',
            schemaFile: options.schemaFile,
            exampleFile: options.exampleFile,
            configFile: options.configFile,
            mode: options.mode || 'example',
            outputFormat: options.format || 'text',
            strict: options.strict || false,
            verbose: options.verbose || false,
            exitOnError: options.exitOnError !== false,
            ignorePatterns: options.ignorePatterns ? options.ignorePatterns.split(',') : [],
        };
    }
    /**
     * Load configuration
     */
    async loadConfig(cliOptions) {
        const configLoader = new loader_1.ConfigLoader();
        // Load from config file if specified
        let config = {};
        if (cliOptions.configFile) {
            config = await configLoader.loadFromFile(cliOptions.configFile);
        }
        else {
            // Try to load default config files
            config = await configLoader.loadDefault();
        }
        // Override with CLI options
        return {
            ...config,
            envFile: cliOptions.envFile,
            schemaFile: cliOptions.schemaFile || config.schemaFile,
            exampleFile: cliOptions.exampleFile || config.exampleFile,
            outputFormat: cliOptions.outputFormat,
            strict: cliOptions.strict,
            verbose: cliOptions.verbose,
            exitOnError: cliOptions.exitOnError,
            ignorePatterns: [
                ...(config.ignorePatterns || []),
                ...cliOptions.ignorePatterns,
            ],
        };
    }
    /**
     * Run the CLI
     */
    async run(argv) {
        try {
            // Show banner for help command or when no arguments
            const args = argv || process.argv;
            if (args.length <= 2 || args.includes('--help') || args.includes('-h')) {
                this.displayBanner();
            }
            await this.program.parseAsync(argv);
        }
        catch (error) {
            console.error(chalk_1.default.red(`Error: ${error.message}`));
            process.exit(1);
        }
    }
}
exports.EnvGuardCLI = EnvGuardCLI;
// Run CLI if this file is executed directly
if (require.main === module) {
    const cli = new EnvGuardCLI();
    cli.run().catch((error) => {
        console.error(chalk_1.default.red(`Fatal error: ${error.message}`));
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map