import { ValidationResult, EnvGuardConfig } from '../types';
/**
 * Main validator that orchestrates all validation components
 */
export declare class EnvGuardValidator {
    private config;
    constructor(config: EnvGuardConfig);
    /**
     * Validate environment file
     */
    validate(): Promise<ValidationResult>;
    /**
     * Validate using schema file
     */
    private validateWithSchema;
    /**
     * Validate using example file
     */
    private validateWithExample;
    /**
     * Validate using both schema and example
     */
    private validateWithBoth;
    /**
     * Perform security analysis
     */
    private performSecurityAnalysis;
    /**
     * Determine validation mode based on config
     */
    private determineValidationMode;
    /**
     * Load schema from file
     */
    private loadSchema;
    /**
     * Create error result from parse errors
     */
    private createErrorResult;
    /**
     * Merge validation summaries
     */
    private mergeSummaries;
    /**
     * Remove duplicate errors
     */
    private deduplicateErrors;
    /**
     * Remove duplicate warnings
     */
    private deduplicateWarnings;
}
//# sourceMappingURL=validator.d.ts.map