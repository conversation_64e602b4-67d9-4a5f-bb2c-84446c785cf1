{"version": 3, "file": "templates.js", "sourceRoot": "", "sources": ["../../src/utils/templates.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,oDAqFC;AAKD,oDAuCC;AAKD,sDAmDC;AAKD,oEAmDC;AAKD,kEAkCC;AAlSD,2BAA+C;AAG/C;;GAEG;AAEH;;GAEG;AACI,KAAK,UAAU,oBAAoB,CAAC,QAAQ,GAAG,sBAAsB;IAC1E,IAAI,IAAA,eAAU,EAAC,QAAQ,CAAC,EAAE,CAAC;QACzB,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,iBAAiB,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,MAAM,GAAc;QACxB,SAAS,EAAE;YACT,QAAQ,EAAE;gBACR,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,qBAAqB;gBAClC,OAAO,EAAE,aAAa;gBACtB,UAAU,EAAE,KAAK;aAClB;YACD,IAAI,EAAE;gBACJ,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oBAAoB;gBACjC,OAAO,EAAE,MAAM;gBACf,UAAU,EAAE,KAAK;aAClB;YACD,YAAY,EAAE;gBACZ,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,yBAAyB;gBACtC,OAAO,EAAE,kDAAkD;gBAC3D,UAAU,EAAE,KAAK;aAClB;YACD,UAAU,EAAE;gBACV,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oBAAoB;gBACjC,OAAO,EAAE,2BAA2B;gBACpC,UAAU,EAAE,KAAK;aAClB;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,kBAAkB;gBAC/B,OAAO,EAAE,mBAAmB;gBAC5B,UAAU,EAAE,IAAI;aACjB;YACD,KAAK,EAAE;gBACL,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,mBAAmB;gBAChC,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,IAAI;aACjB;SACF;QACD,KAAK,EAAE;YACL;gBACE,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,kCAAkC;gBAC/C,QAAQ,EAAE,OAAO;gBACjB,OAAO,EAAE,qCAAqC;aAC/C;YACD;gBACE,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,sDAAsD;gBACnE,QAAQ,EAAE,SAAS;gBACnB,eAAe,EAAE,CAAC,MAAc,EAAE,EAAE;oBAClC,oDAAoD;oBACpD,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;SACF;QACD,aAAa,EAAE;YACb;gBACE,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,+BAA+B;gBAC5C,OAAO,EAAE,sDAAsD;gBAC/D,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,6CAA6C;aAC1D;SACF;QACD,cAAc,EAAE;YACd,MAAM;YACN,UAAU;YACV,WAAW;SACZ;KACF,CAAC;IAEF,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChD,IAAA,kBAAa,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,oBAAoB,CAAC,QAAQ,GAAG,sBAAsB;IAC1E,IAAI,IAAA,eAAU,EAAC,QAAQ,CAAC,EAAE,CAAC;QACzB,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,iBAAiB,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,MAAM,GAAG;QACb,OAAO,EAAE,MAAM;QACf,UAAU,EAAE,sBAAsB;QAClC,WAAW,EAAE,cAAc;QAC3B,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,KAAK;QACd,WAAW,EAAE,IAAI;QACjB,cAAc,EAAE;YACd,MAAM;YACN,UAAU;YACV,WAAW;SACZ;QACD,WAAW,EAAE;YACX;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,iDAAiD;gBAC9D,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,2BAA2B;aACrC;SACF;QACD,aAAa,EAAE;YACb;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,8BAA8B;gBAC3C,QAAQ,EAAE,OAAO;gBACjB,OAAO,EAAE,sCAAsC;gBAC/C,UAAU,EAAE,2CAA2C;aACxD;SACF;KACF,CAAC;IAEF,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChD,IAAA,kBAAa,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,qBAAqB,CAAC,QAAQ,GAAG,cAAc;IACnE,IAAI,IAAA,eAAU,EAAC,QAAQ,CAAC,EAAE,CAAC;QACzB,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,iBAAiB,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2CjB,CAAC;IAEA,IAAA,kBAAa,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,4BAA4B,CAAC,QAAQ,GAAG,gCAAgC;IAC5F,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsCjB,CAAC;IAEA,uCAAuC;IACvC,MAAM,EAAE,GAAG,wDAAa,IAAI,GAAC,CAAC;IAC9B,MAAM,IAAI,GAAG,wDAAa,MAAM,GAAC,CAAC;IAElC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACnC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACxB,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACzC,CAAC;IAED,IAAA,kBAAa,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,2BAA2B,CAAC,QAAQ,GAAG,uBAAuB;IAClF,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BjB,CAAC;IAEA,IAAA,kBAAa,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAE1C,2BAA2B;IAC3B,MAAM,EAAE,GAAG,wDAAa,IAAI,GAAC,CAAC;IAC9B,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAChC,CAAC"}