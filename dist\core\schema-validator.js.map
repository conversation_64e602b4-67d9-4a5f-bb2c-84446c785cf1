{"version": 3, "file": "schema-validator.js", "sourceRoot": "", "sources": ["../../src/core/schema-validator.ts"], "names": [], "mappings": ";;;AAWA;;GAEG;AACH,MAAa,eAAe;IAG1B,YAAY,MAAiB;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,SAAwB;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,MAAM,QAAQ,GAAwB,EAAE,CAAC;QACzC,MAAM,IAAI,GAAqB,EAAE,CAAC;QAElC,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;QAE/C,uCAAuC;QACvC,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;QAEhE,6BAA6B;QAC7B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAEzE,4CAA4C;QAC5C,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAEzD,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEzD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEhF,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;YACN,QAAQ;YACR,IAAI;YACJ,OAAO;SACR,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,yBAAyB,CAC/B,UAAoB,EACpB,WAAqC,EACrC,MAAyB;QAEzB,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,MAAM,EAAE,QAAQ,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,kBAAkB;oBACxB,QAAQ,EAAE,GAAG;oBACb,OAAO,EAAE,kCAAkC,GAAG,cAAc;oBAC5D,UAAU,EAAE,MAAM,CAAC,WAAW;wBAC5B,CAAC,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,OAAO,IAAI,SAAS,MAAM,MAAM,CAAC,WAAW,EAAE;wBACrE,CAAC,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,OAAO,IAAI,SAAS,EAAE;oBAC/C,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAC7B,OAAiB,EACjB,UAAoB,EACpB,WAAqC,EACrC,QAA6B;QAE7B,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;QACzC,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC;QAExD,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE,CAAC;gBAC9E,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;gBACvC,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,iBAAiB;oBACvB,QAAQ,EAAE,GAAG;oBACb,OAAO,EAAE,aAAa,GAAG,4BAA4B;oBACrD,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,+CAA+C;oBAC3D,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAC5B,SAAwB,EACxB,MAAyB,EACzB,QAA6B;QAE7B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM;gBAAE,SAAS;YAEtB,sBAAsB;YACtB,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACtB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,YAAY;oBAClB,QAAQ,EAAE,QAAQ,CAAC,GAAG;oBACtB,OAAO,EAAE,aAAa,QAAQ,CAAC,GAAG,iBAAiB;oBACnD,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,MAAM,CAAC,YAAY,EAAE,MAAM;wBACrC,CAAC,CAAC,mBAAmB,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACrD,CAAC,CAAC,iCAAiC;oBACrC,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;YAED,qBAAqB;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBACvD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,gBAAgB;oBACtB,QAAQ,EAAE,QAAQ,CAAC,GAAG;oBACtB,OAAO,EAAE,aAAa,QAAQ,CAAC,GAAG,mBAAmB;oBACrD,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS;oBACrE,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,gBAAgB;YAChB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAEpD,mBAAmB;YACnB,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAC1B,QAAqB,EACrB,MAAsB,EACtB,MAAyB;QAEzB,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE;YAAE,OAAO;QAEzD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC7B,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,cAAc,GAAG,EAAE,CAAC;QAExB,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,KAAK,QAAQ;gBACX,OAAO,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC3D,cAAc,GAAG,gBAAgB,CAAC;gBAClC,MAAM;YACR,KAAK,SAAS;gBACZ,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;gBACjF,cAAc,GAAG,+BAA+B,CAAC;gBACjD,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,CAAC;oBACH,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC;gBAAC,MAAM,CAAC;oBACP,OAAO,GAAG,KAAK,CAAC;gBAClB,CAAC;gBACD,cAAc,GAAG,aAAa,CAAC;gBAC/B,MAAM;YACR,KAAK,OAAO;gBACV,OAAO,GAAG,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnD,cAAc,GAAG,uBAAuB,CAAC;gBACzC,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC;oBACH,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACpB,CAAC;gBAAC,MAAM,CAAC;oBACP,OAAO,GAAG,KAAK,CAAC;gBAClB,CAAC;gBACD,cAAc,GAAG,YAAY,CAAC;gBAC9B,MAAM;QACV,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,QAAQ,CAAC,GAAG;gBACtB,OAAO,EAAE,aAAa,QAAQ,CAAC,GAAG,aAAa,cAAc,EAAE;gBAC/D,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS;gBACrE,QAAQ,EAAE,OAAO;aAClB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAC7B,QAAqB,EACrB,MAAsB,EACtB,MAAyB;QAEzB,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE;YAAE,OAAO;QAE5D,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,QAAQ,CAAC,GAAG;gBACtB,OAAO,EAAE,aAAa,QAAQ,CAAC,GAAG,mCAAmC;gBACrE,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS;gBACrE,QAAQ,EAAE,OAAO;aAClB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CACtB,SAAwB,EACxB,MAAyB,EACzB,QAA6B,EAC7B,IAAsB;QAEtB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK;YAAE,OAAO;QAE/B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACrC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBACvD,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACtB,KAAK,OAAO;4BACV,MAAM,CAAC,IAAI,CAAC;gCACV,IAAI,EAAE,QAAQ;gCACd,QAAQ,EAAE,QAAQ,CAAC,GAAG;gCACtB,OAAO,EAAE,IAAI,CAAC,WAAW;gCACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;gCAC/B,QAAQ,EAAE,OAAO;6BAClB,CAAC,CAAC;4BACH,MAAM;wBACR,KAAK,SAAS;4BACZ,QAAQ,CAAC,IAAI,CAAC;gCACZ,IAAI,EAAE,QAAQ;gCACd,QAAQ,EAAE,QAAQ,CAAC,GAAG;gCACtB,OAAO,EAAE,IAAI,CAAC,WAAW;gCACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;gCAC/B,QAAQ,EAAE,SAAS;6BACpB,CAAC,CAAC;4BACH,MAAM;wBACR,KAAK,MAAM;4BACT,IAAI,CAAC,IAAI,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,QAAQ,EAAE,QAAQ,CAAC,GAAG;gCACtB,OAAO,EAAE,IAAI,CAAC,WAAW;gCACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;gCAC/B,QAAQ,EAAE,MAAM;6BACjB,CAAC,CAAC;4BACH,MAAM;oBACV,CAAC;gBACH,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBAClE,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACtB,KAAK,OAAO;4BACV,MAAM,CAAC,IAAI,CAAC;gCACV,IAAI,EAAE,QAAQ;gCACd,QAAQ,EAAE,QAAQ,CAAC,GAAG;gCACtB,OAAO,EAAE,IAAI,CAAC,WAAW;gCACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;gCAC/B,QAAQ,EAAE,OAAO;6BAClB,CAAC,CAAC;4BACH,MAAM;wBACR,KAAK,SAAS;4BACZ,QAAQ,CAAC,IAAI,CAAC;gCACZ,IAAI,EAAE,QAAQ;gCACd,QAAQ,EAAE,QAAQ,CAAC,GAAG;gCACtB,OAAO,EAAE,IAAI,CAAC,WAAW;gCACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;gCAC/B,QAAQ,EAAE,SAAS;6BACpB,CAAC,CAAC;4BACH,MAAM;wBACR,KAAK,MAAM;4BACT,IAAI,CAAC,IAAI,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,QAAQ,EAAE,QAAQ,CAAC,GAAG;gCACtB,OAAO,EAAE,IAAI,CAAC,WAAW;gCACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;gCAC/B,QAAQ,EAAE,MAAM;6BACjB,CAAC,CAAC;4BACH,MAAM;oBACV,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,GAAW,EAAE,cAAwB;QAChE,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACnC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;YAClC,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa,CACnB,SAAwB,EACxB,MAAyB,EACzB,QAA6B,EAC7B,cAAsB;QAEtB,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QAC9F,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,kBAAkB,CAAC,CAAC,MAAM,CAAC;QAClF,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,CAAC,MAAM,CAAC;QAClF,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC,MAAM,CAAC;QAE7E,OAAO;YACL,cAAc,EAAE,SAAS,CAAC,MAAM;YAChC,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;YACf,cAAc;YACd,cAAc;SACf,CAAC;IACJ,CAAC;CACF;AA/UD,0CA+UC"}