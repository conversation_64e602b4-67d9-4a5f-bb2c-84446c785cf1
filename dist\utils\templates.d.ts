/**
 * Template generators for EnvGuard configuration files
 */
/**
 * Create schema template file
 */
export declare function createSchemaTemplate(filename?: string): Promise<void>;
/**
 * Create config template file
 */
export declare function createConfigTemplate(filename?: string): Promise<void>;
/**
 * Create .env.example template
 */
export declare function createExampleTemplate(filename?: string): Promise<void>;
/**
 * Create GitHub Actions workflow template
 */
export declare function createGitHubWorkflowTemplate(filename?: string): Promise<void>;
/**
 * Create pre-commit hook template
 */
export declare function createPreCommitHookTemplate(filename?: string): Promise<void>;
//# sourceMappingURL=templates.d.ts.map