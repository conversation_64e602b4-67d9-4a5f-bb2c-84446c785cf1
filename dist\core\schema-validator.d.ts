import { EnvVariable, EnvSchema, ValidationResult } from '../types';
/**
 * Schema-based validator for environment variables
 */
export declare class SchemaValidator {
    private schema;
    constructor(schema: EnvSchema);
    /**
     * Validate environment variables against the schema
     */
    validate(variables: EnvVariable[]): ValidationResult;
    /**
     * Validate that all required variables are present
     */
    private validateRequiredVariables;
    /**
     * Validate for unused variables
     */
    private validateUnusedVariables;
    /**
     * Validate individual variable values against their schemas
     */
    private validateVariableValues;
    /**
     * Validate variable type
     */
    private validateVariableType;
    /**
     * Validate variable pattern
     */
    private validateVariablePattern;
    /**
     * Apply custom validation rules
     */
    private applyCustomRules;
    /**
     * Check if variable should be ignored based on patterns
     */
    private shouldIgnoreVariable;
    /**
     * Create validation summary
     */
    private createSummary;
}
//# sourceMappingURL=schema-validator.d.ts.map