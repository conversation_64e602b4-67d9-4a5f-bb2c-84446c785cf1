"use strict";
/**
 * EnvGuard - Environment File Validator
 * Main entry point for programmatic usage
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.OutputFormatter = exports.EnvGuardCLI = exports.ConfigLoader = exports.SecurityAnalyzer = exports.EnvGuardValidator = exports.ExampleValidator = exports.SchemaValidator = exports.EnvParser = void 0;
// Core exports
var parser_1 = require("./core/parser");
Object.defineProperty(exports, "EnvParser", { enumerable: true, get: function () { return parser_1.EnvParser; } });
var schema_validator_1 = require("./core/schema-validator");
Object.defineProperty(exports, "SchemaValidator", { enumerable: true, get: function () { return schema_validator_1.SchemaValidator; } });
var example_validator_1 = require("./core/example-validator");
Object.defineProperty(exports, "ExampleValidator", { enumerable: true, get: function () { return example_validator_1.ExampleValidator; } });
var validator_1 = require("./core/validator");
Object.defineProperty(exports, "EnvGuardValidator", { enumerable: true, get: function () { return validator_1.EnvGuardValidator; } });
// Security exports
var analyzer_1 = require("./security/analyzer");
Object.defineProperty(exports, "SecurityAnalyzer", { enumerable: true, get: function () { return analyzer_1.SecurityAnalyzer; } });
// Configuration exports
var loader_1 = require("./config/loader");
Object.defineProperty(exports, "ConfigLoader", { enumerable: true, get: function () { return loader_1.ConfigLoader; } });
// CLI exports
var index_1 = require("./cli/index");
Object.defineProperty(exports, "EnvGuardCLI", { enumerable: true, get: function () { return index_1.EnvGuardCLI; } });
var output_formatter_1 = require("./cli/output-formatter");
Object.defineProperty(exports, "OutputFormatter", { enumerable: true, get: function () { return output_formatter_1.OutputFormatter; } });
// Utility exports
__exportStar(require("./utils/templates"), exports);
// Type exports
__exportStar(require("./types"), exports);
// Default export for convenience
var validator_2 = require("./core/validator");
Object.defineProperty(exports, "default", { enumerable: true, get: function () { return validator_2.EnvGuardValidator; } });
//# sourceMappingURL=index.js.map