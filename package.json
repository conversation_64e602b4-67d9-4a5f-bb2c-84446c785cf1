{"name": "envguard", "version": "1.0.0", "description": "A comprehensive environment file validator with security analysis and CI/CD integration", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"envguard": "dist/cli.js"}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "ts-node src/cli.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "prepare": "npm run build", "prepublishOnly": "npm test && npm run lint"}, "keywords": ["environment", "validation", "env", "dotenv", "security", "cli", "ci-cd"], "author": "EnvGuard Team", "license": "MIT", "files": ["dist", "README.md", "LICENSE"], "engines": {"node": ">=14.0.0"}, "dependencies": {"commander": "^11.1.0", "chalk": "^4.1.2", "dotenv": "^16.3.1", "joi": "^17.11.0", "yaml": "^2.3.4"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.test.ts"], "testPathIgnorePatterns": ["fixtures"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/__tests__/**"]}, "repository": {"type": "git", "url": "https://github.com/envguard/envguard.git"}, "bugs": {"url": "https://github.com/envguard/envguard/issues"}, "homepage": "https://github.com/envguard/envguard#readme"}