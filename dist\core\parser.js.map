{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../src/core/parser.ts"], "names": [], "mappings": ";;;AAAA,2BAAkC;AAGlC;;GAEG;AACH,MAAa,SAAS;IACpB;;OAEG;IACI,MAAM,CAAC,SAAS,CAAC,QAAgB;QACtC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,KAAM,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,OAAe;QACxC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,MAAM,SAAS,GAAkB,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,MAAM,WAAW,GAAiB,EAAE,CAAC;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;YACzB,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,IAAI;gBAAE,SAAS;YACpB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAEhC,mBAAmB;YACnB,IAAI,WAAW,KAAK,EAAE,EAAE,CAAC;gBACvB,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC5B,SAAS;YACX,CAAC;YAED,kBAAkB;YAClB,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC/C,SAAS;YACX,CAAC;YAED,4BAA4B;YAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YACxD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;iBAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC3B,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,OAAO;YACL,SAAS;YACT,QAAQ;YACR,UAAU;YACV,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAC9B,IAAY,EACZ,UAAkB;QAElB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAEhC,wCAAwC;QACxC,MAAM,eAAe,GAAG,WAAW,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACnF,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO;gBACL,KAAK,EAAE;oBACL,UAAU;oBACV,OAAO,EAAE,oCAAoC;oBAC7C,IAAI,EAAE,WAAW;iBAClB;aACF,CAAC;QACJ,CAAC;QAED,MAAM,GAAG,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,gBAAgB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;QAE5C,IAAI,CAAC,GAAG,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC3C,OAAO;gBACL,KAAK,EAAE;oBACL,UAAU;oBACV,OAAO,EAAE,oCAAoC;oBAC7C,IAAI,EAAE,WAAW;iBAClB;aACF,CAAC;QACJ,CAAC;QAED,mCAAmC;QACnC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;QACzE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE3D,OAAO;YACL,QAAQ,EAAE;gBACR,GAAG;gBACH,KAAK,EAAE,UAAU;gBACjB,UAAU;gBACV,QAAQ;gBACR,UAAU,EAAE,OAAO;aACpB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CAAC,gBAAwB;QAI5D,uDAAuD;QACvD,MAAM,WAAW,GAAG,gBAAgB,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QACvE,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,CAAC,EAAE,AAAD,EAAG,WAAW,EAAE,WAAW,CAAC,GAAG,WAAW,CAAC;YACnD,OAAO;gBACL,KAAK,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE;gBACzD,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;aACjD,CAAC;QACJ,CAAC;QAED,yBAAyB;QACzB,MAAM,YAAY,GAAG,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnD,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;YACxB,OAAO,EAAE,KAAK,EAAE,gBAAgB,CAAC,IAAI,EAAE,EAAE,CAAC;QAC5C,CAAC;QAED,OAAO;YACL,KAAK,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,IAAI,EAAE;YACzD,OAAO,EAAE,gBAAgB,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;SAC7D,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,aAAa,CAAC,KAAa;QACxC,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAElC,0BAA0B;QAC1B,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC3F,OAAO;gBACL,UAAU,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrC,QAAQ,EAAE,IAAI;aACf,CAAC;QACJ,CAAC;QAED,0BAA0B;QAC1B,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC3F,2CAA2C;YAC3C,MAAM,SAAS,GAAG,YAAY;iBAC3B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACZ,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;iBACrB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;iBACrB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;iBACrB,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;iBACtB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAExB,OAAO;gBACL,UAAU,EAAE,SAAS;gBACrB,QAAQ,EAAE,IAAI;aACf,CAAC;QACJ,CAAC;QAED,OAAO;YACL,UAAU,EAAE,YAAY;YACxB,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CAAC,SAAwB;QAC9C,OAAO,SAAS;aACb,GAAG,CAAC,QAAQ,CAAC,EAAE;YACd,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;YACzE,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACvE,OAAO,GAAG,QAAQ,CAAC,GAAG,IAAI,KAAK,GAAG,OAAO,EAAE,CAAC;QAC9C,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;CACF;AAzLD,8BAyLC"}