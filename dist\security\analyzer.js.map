{"version": 3, "file": "analyzer.js", "sourceRoot": "", "sources": ["../../src/security/analyzer.ts"], "names": [], "mappings": ";;;AAQA;;GAEG;AACH,MAAa,gBAAgB;IAG3B,YAAY,cAA8B,EAAE;QAC1C,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,uBAAuB,EAAE,EAAE,GAAG,WAAW,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACI,OAAO,CACZ,SAAwB;QAMxB,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,MAAM,QAAQ,GAAwB,EAAE,CAAC;QACzC,MAAM,IAAI,GAAqB,EAAE,CAAC;QAElC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,+BAA+B;YAC/B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE1D,8BAA8B;YAC9B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YAEvD,0BAA0B;YAC1B,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAEjD,mCAAmC;YACnC,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,OAAO;YACL;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,6BAA6B;gBAC1C,OAAO,EAAE,sDAAsD;gBAC/D,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,qDAAqD;aAClE;YACD;gBACE,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,sCAAsC;gBACnD,OAAO,EAAE,mCAAmC;gBAC5C,QAAQ,EAAE,SAAS;gBACnB,UAAU,EAAE,sDAAsD;aACnE;YACD;gBACE,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,qCAAqC;gBAClD,OAAO,EAAE,8BAA8B;gBACvC,QAAQ,EAAE,SAAS;gBACnB,UAAU,EAAE,qDAAqD;aAClE;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,0BAA0B;gBACvC,OAAO,EAAE,6CAA6C;gBACtD,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,6CAA6C;aAC1D;YACD;gBACE,IAAI,EAAE,8BAA8B;gBACpC,WAAW,EAAE,uCAAuC;gBACpD,OAAO,EAAE,6EAA6E;gBACtF,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,yCAAyC;aACtD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB,CACxB,QAAqB,EACrB,MAAyB,EACzB,QAA6B,EAC7B,IAAsB;QAEtB,MAAM,YAAY,GAAG,GAAG,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;QAEzD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACtC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBACpC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACtB,KAAK,OAAO;wBACV,MAAM,CAAC,IAAI,CAAC;4BACV,IAAI,EAAE,eAAe;4BACrB,QAAQ,EAAE,QAAQ,CAAC,GAAG;4BACtB,OAAO,EAAE,IAAI,CAAC,WAAW;4BACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;4BAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;4BAC3B,QAAQ,EAAE,OAAO;yBAClB,CAAC,CAAC;wBACH,MAAM;oBACR,KAAK,SAAS;wBACZ,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,eAAe;4BACrB,QAAQ,EAAE,QAAQ,CAAC,GAAG;4BACtB,OAAO,EAAE,IAAI,CAAC,WAAW;4BACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;4BAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;4BAC3B,QAAQ,EAAE,SAAS;yBACpB,CAAC,CAAC;wBACH,MAAM;oBACR,KAAK,MAAM;wBACT,IAAI,CAAC,IAAI,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,QAAQ,EAAE,QAAQ,CAAC,GAAG;4BACtB,OAAO,EAAE,IAAI,CAAC,WAAW;4BACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;4BAC/B,QAAQ,EAAE,MAAM;yBACjB,CAAC,CAAC;wBACH,MAAM;gBACV,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAC3B,QAAqB,EACrB,MAAyB,EACzB,QAA6B;QAE7B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC7B,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;QAEvC,mCAAmC;QACnC,MAAM,cAAc,GAAG;YACrB,EAAE,OAAO,EAAE,4BAA4B,EAAE,IAAI,EAAE,uBAAuB,EAAE;YACxE,EAAE,OAAO,EAAE,kBAAkB,EAAE,IAAI,EAAE,oBAAoB,EAAE;YAC3D,EAAE,OAAO,EAAE,sBAAsB,EAAE,IAAI,EAAE,sBAAsB,EAAE;SAClE,CAAC;QAEF,MAAM,aAAa,GAAG;YACpB,QAAQ;YACR,KAAK;YACL,OAAO;YACP,UAAU;YACV,MAAM;YACN,KAAK;YACL,MAAM;YACN,YAAY;YACZ,SAAS;SACV,CAAC;QAEF,MAAM,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;QAEtF,IAAI,cAAc,EAAE,CAAC;YACnB,uCAAuC;YACvC,MAAM,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACvE,IAAI,cAAc,EAAE,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,QAAQ,CAAC,GAAG;oBACtB,OAAO,EAAE,aAAa,cAAc,CAAC,IAAI,iCAAiC;oBAC1E,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,wDAAwD;oBACpE,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;YAED,kCAAkC;YAClC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7B,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,QAAQ,CAAC,GAAG;oBACtB,OAAO,EAAE,iCAAiC;oBAC1C,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,yCAAyC;oBACrD,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CACvB,QAAqB,EACrB,QAA6B,EAC7B,IAAsB;QAEtB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC7B,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;QAEvC,wCAAwC;QACxC,MAAM,WAAW,GAAG;YAClB,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,wBAAwB,EAAE;YAC5D,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,uBAAuB,EAAE;YAC7D,EAAE,OAAO,EAAE,2BAA2B,EAAE,OAAO,EAAE,wCAAwC,EAAE;YAC3F,EAAE,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,MAAe,EAAE;SAC5F,CAAC;QAEF,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,SAAS,CAAC;gBAElD,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBAC3B,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,cAAc;wBACpB,QAAQ,EAAE,QAAQ,CAAC,GAAG;wBACtB,OAAO,EAAE,UAAU,CAAC,OAAO;wBAC3B,UAAU,EAAE,QAAQ,CAAC,UAAU;wBAC/B,UAAU,EAAE,2CAA2C;wBACvD,QAAQ,EAAE,SAAS;qBACpB,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,IAAI,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE,QAAQ,CAAC,GAAG;wBACtB,OAAO,EAAE,UAAU,CAAC,OAAO;wBAC3B,UAAU,EAAE,QAAQ,CAAC,UAAU;wBAC/B,QAAQ,EAAE,MAAM;qBACjB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,0DAA0D;QAC1D,MAAM,aAAa,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;QACrE,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;QAEnF,IAAI,WAAW,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YAClD,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,cAAc;gBACpB,QAAQ,EAAE,QAAQ,CAAC,GAAG;gBACtB,OAAO,EAAE,kDAAkD;gBAC3D,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,UAAU,EAAE,kCAAkC;gBAC9C,QAAQ,EAAE,SAAS;aACpB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,0BAA0B,CAChC,QAAqB,EACrB,QAA6B;QAE7B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAE7B,4CAA4C;QAC5C,MAAM,iBAAiB,GAAG;YACxB,EAAE,OAAO,EAAE,4CAA4C,EAAE,OAAO,EAAE,4BAA4B,EAAE;YAChG,EAAE,OAAO,EAAE,uBAAuB,EAAE,OAAO,EAAE,aAAa,EAAE;YAC5D,EAAE,OAAO,EAAE,gDAAgD,EAAE,OAAO,EAAE,eAAe,EAAE;YACvF,EAAE,OAAO,EAAE,wCAAwC,EAAE,OAAO,EAAE,YAAY,EAAE;SAC7E,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,QAAQ,CAAC,GAAG;oBACtB,OAAO,EAAE,aAAa,OAAO,CAAC,OAAO,WAAW;oBAChD,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,+DAA+D;oBAC3E,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;YAC3B,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACjC,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,QAAQ,CAAC,GAAG;oBACtB,OAAO,EAAE,mCAAmC;oBAC5C,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,wCAAwC;oBACpD,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,oBAAoB;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,KAAa;QAChC,MAAM,WAAW,GAAG;YAClB,QAAQ;YACR,UAAU;YACV,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,KAAK;YACL,aAAa;YACb,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,UAAU;YACV,SAAS;SACV,CAAC;QAEF,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,OAAO,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAa;QACtC,MAAM,mBAAmB,GAAG;YAC1B,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,eAAe;YACf,SAAS;YACT,SAAS;YACT,UAAU;SACX,CAAC;QAEF,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACzE,CAAC;CACF;AArVD,4CAqVC"}