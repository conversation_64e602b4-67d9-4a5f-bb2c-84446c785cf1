{"version": 3, "file": "output-formatter.js", "sourceRoot": "", "sources": ["../../src/cli/output-formatter.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAG1B;;GAEG;AACH,MAAa,eAAe;IAG1B,YAAY,SAAoC,MAAM;QACpD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAwB;QACpC,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;YAC1B,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACjC,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAClC,KAAK,MAAM,CAAC;YACZ;gBACE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,MAAwB;QACzC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,iBAAiB;QACjB,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,eAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,eAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChF,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,uCAAuC,CAAC,CAAC;QAE7D,SAAS;QACT,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,KAAK,CAAC,IAAI,CAAC,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;YACnE,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;YACtC,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;QAED,WAAW;QACX,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,KAAK,CAAC,IAAI,CAAC,eAAK,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,MAAM,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;YAC3E,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACtC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC1C,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;QAED,OAAO;QACP,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,KAAK,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;YACjE,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC/B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YACpC,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;QAED,UAAU;QACV,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QAEvC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAsB;QACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5E,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClE,MAAM,OAAO,GAAG,eAAK,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzC,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEtF,OAAO,KAAK,QAAQ,GAAG,QAAQ,KAAK,OAAO,GAAG,UAAU,EAAE,CAAC;IAC7D,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,OAA0B;QAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChF,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtE,MAAM,OAAO,GAAG,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC9C,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE1F,OAAO,KAAK,QAAQ,GAAG,QAAQ,KAAK,OAAO,GAAG,UAAU,EAAE,CAAC;IAC7D,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,IAAoB;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1E,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChE,MAAM,OAAO,GAAG,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzC,OAAO,KAAK,QAAQ,GAAG,QAAQ,KAAK,OAAO,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,MAAwB;QAC5C,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;QACtC,KAAK,CAAC,IAAI,CAAC,sBAAsB,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;QAClF,KAAK,CAAC,IAAI,CAAC,yBAAyB,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;QAExF,IAAI,OAAO,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,wBAAwB,eAAK,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YAChC,KAAK,CAAC,IAAI,CAAC,uBAAuB,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;QACxF,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YAC/B,KAAK,CAAC,IAAI,CAAC,sBAAsB,eAAK,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;QACnF,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,sBAAsB,eAAK,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC,CAAC;QAE9E,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,MAAwB;QACzC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,MAAwB;QAC1C,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACtF,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;QACtC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/D,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,KAAK,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACrD,KAAK,CAAC,IAAI,CAAC,qCAAqC,UAAU,eAAe,QAAQ,WAAW,IAAI,IAAI,CAAC,CAAC;QAEtG,4BAA4B;QAC5B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,8BAA8B;QAC9B,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACtC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QAC3D,CAAC;QAED,0BAA0B;QAC1B,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YAC/B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;QACrD,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,KAA2D,EAC3D,IAAkC;QAElC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,SAAS,CAAC;QAC7C,MAAM,SAAS,GAAG,YAAY,KAAK,CAAC,IAAI,EAAE,CAAC;QAC3C,MAAM,QAAQ,GAAG,GAAG,QAAQ,IAAI,IAAI,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;QAExE,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;YACrB,OAAO,0BAA0B,SAAS,WAAW,QAAQ;wBAC3C,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,KAAK,CAAC,IAAI;QAClE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC;QAC7B,UAAU,CAAC,CAAC,CAAC,iBAAiB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;;cAEzD,CAAC;QACX,CAAC;aAAM,CAAC;YACN,OAAO,0BAA0B,SAAS,WAAW,QAAQ;kBACjD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC;cACjC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,IAAY;QAC5B,OAAO,IAAI;aACR,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;aACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;aACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC7B,CAAC;CACF;AA3MD,0CA2MC"}