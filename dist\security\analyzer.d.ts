import { EnvVariable, SecurityRule, ValidationError, ValidationWarning, ValidationInfo } from '../types';
/**
 * Security analyzer for environment variables
 */
export declare class SecurityAnalyzer {
    private securityRules;
    constructor(customRules?: SecurityRule[]);
    /**
     * Analyze environment variables for security issues
     */
    analyze(variables: EnvVariable[]): {
        errors: ValidationError[];
        warnings: ValidationWarning[];
        info: ValidationInfo[];
    };
    /**
     * Get default security rules
     */
    private getDefaultSecurityRules;
    /**
     * Check variable against security rules
     */
    private checkSecurityRules;
    /**
     * Check for hardcoded secrets
     */
    private checkHardcodedSecrets;
    /**
     * Check for weak patterns
     */
    private checkWeakPatterns;
    /**
     * Check for sensitive data exposure
     */
    private checkSensitiveDataExposure;
    /**
     * Check if a value is a weak secret
     */
    private isWeakSecret;
    /**
     * Check if a value is a placeholder
     */
    private isPlaceholderValue;
}
//# sourceMappingURL=analyzer.d.ts.map