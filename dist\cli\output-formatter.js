"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OutputFormatter = void 0;
const chalk_1 = __importDefault(require("chalk"));
/**
 * Output formatter for validation results
 */
class OutputFormatter {
    constructor(format = 'text') {
        this.outputFormat = format;
    }
    /**
     * Format validation result
     */
    format(result) {
        switch (this.outputFormat) {
            case 'json':
                return this.formatJson(result);
            case 'junit':
                return this.formatJUnit(result);
            case 'text':
            default:
                return this.formatText(result);
        }
    }
    /**
     * Format as human-readable text
     */
    formatText(result) {
        const lines = [];
        // Summary header
        const status = result.isValid ? chalk_1.default.green('✅ PASSED') : chalk_1.default.red('❌ FAILED');
        lines.push(`${status} - Environment validation completed\n`);
        // Errors
        if (result.errors.length > 0) {
            lines.push(chalk_1.default.red.bold(`🚨 ERRORS (${result.errors.length}):`));
            for (const error of result.errors) {
                lines.push(this.formatError(error));
            }
            lines.push('');
        }
        // Warnings
        if (result.warnings.length > 0) {
            lines.push(chalk_1.default.yellow.bold(`⚠️  WARNINGS (${result.warnings.length}):`));
            for (const warning of result.warnings) {
                lines.push(this.formatWarning(warning));
            }
            lines.push('');
        }
        // Info
        if (result.info.length > 0) {
            lines.push(chalk_1.default.blue.bold(`ℹ️  INFO (${result.info.length}):`));
            for (const info of result.info) {
                lines.push(this.formatInfo(info));
            }
            lines.push('');
        }
        // Summary
        lines.push(this.formatSummary(result));
        return lines.join('\n');
    }
    /**
     * Format error message
     */
    formatError(error) {
        const location = error.lineNumber ? chalk_1.default.gray(`:${error.lineNumber}`) : '';
        const variable = error.variable ? chalk_1.default.cyan(error.variable) : '';
        const message = chalk_1.default.red(error.message);
        const suggestion = error.suggestion ? chalk_1.default.gray(`\n    💡 ${error.suggestion}`) : '';
        return `  ${variable}${location}: ${message}${suggestion}`;
    }
    /**
     * Format warning message
     */
    formatWarning(warning) {
        const location = warning.lineNumber ? chalk_1.default.gray(`:${warning.lineNumber}`) : '';
        const variable = warning.variable ? chalk_1.default.cyan(warning.variable) : '';
        const message = chalk_1.default.yellow(warning.message);
        const suggestion = warning.suggestion ? chalk_1.default.gray(`\n    💡 ${warning.suggestion}`) : '';
        return `  ${variable}${location}: ${message}${suggestion}`;
    }
    /**
     * Format info message
     */
    formatInfo(info) {
        const location = info.lineNumber ? chalk_1.default.gray(`:${info.lineNumber}`) : '';
        const variable = info.variable ? chalk_1.default.cyan(info.variable) : '';
        const message = chalk_1.default.blue(info.message);
        return `  ${variable}${location}: ${message}`;
    }
    /**
     * Format summary
     */
    formatSummary(result) {
        const { summary } = result;
        const lines = [];
        lines.push(chalk_1.default.bold('📊 SUMMARY:'));
        lines.push(`  Total variables: ${chalk_1.default.cyan(summary.totalVariables.toString())}`);
        lines.push(`  Required variables: ${chalk_1.default.cyan(summary.requiredVariables.toString())}`);
        if (summary.missingVariables > 0) {
            lines.push(`  Missing variables: ${chalk_1.default.red(summary.missingVariables.toString())}`);
        }
        if (summary.unusedVariables > 0) {
            lines.push(`  Unused variables: ${chalk_1.default.yellow(summary.unusedVariables.toString())}`);
        }
        if (summary.securityIssues > 0) {
            lines.push(`  Security issues: ${chalk_1.default.red(summary.securityIssues.toString())}`);
        }
        lines.push(`  Validation time: ${chalk_1.default.gray(`${summary.validationTime}ms`)}`);
        return lines.join('\n');
    }
    /**
     * Format as JSON
     */
    formatJson(result) {
        return JSON.stringify(result, null, 2);
    }
    /**
     * Format as JUnit XML
     */
    formatJUnit(result) {
        const totalTests = result.errors.length + result.warnings.length + result.info.length;
        const failures = result.errors.length;
        const time = (result.summary.validationTime / 1000).toFixed(3);
        const lines = [];
        lines.push('<?xml version="1.0" encoding="UTF-8"?>');
        lines.push(`<testsuite name="EnvGuard" tests="${totalTests}" failures="${failures}" time="${time}">`);
        // Add test cases for errors
        for (const error of result.errors) {
            lines.push(this.formatJUnitTestCase(error, 'error'));
        }
        // Add test cases for warnings
        for (const warning of result.warnings) {
            lines.push(this.formatJUnitTestCase(warning, 'warning'));
        }
        // Add test cases for info
        for (const info of result.info) {
            lines.push(this.formatJUnitTestCase(info, 'info'));
        }
        lines.push('</testsuite>');
        return lines.join('\n');
    }
    /**
     * Format JUnit test case
     */
    formatJUnitTestCase(issue, type) {
        const variable = issue.variable || 'general';
        const className = `EnvGuard.${issue.type}`;
        const testName = `${variable}_${type}`;
        const suggestion = 'suggestion' in issue ? issue.suggestion : undefined;
        if (type === 'error') {
            return `  <testcase classname="${className}" name="${testName}">
    <failure message="${this.escapeXml(issue.message)}" type="${issue.type}">
      ${this.escapeXml(issue.message)}
      ${suggestion ? `\nSuggestion: ${this.escapeXml(suggestion)}` : ''}
    </failure>
  </testcase>`;
        }
        else {
            return `  <testcase classname="${className}" name="${testName}">
    <system-out>${this.escapeXml(issue.message)}</system-out>
  </testcase>`;
        }
    }
    /**
     * Escape XML special characters
     */
    escapeXml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&apos;');
    }
}
exports.OutputFormatter = OutputFormatter;
//# sourceMappingURL=output-formatter.js.map