import { EnvGuardConfig } from '../types';
/**
 * Configuration loader for EnvGuard
 */
export declare class ConfigLoader {
    private static readonly DEFAULT_CONFIG_FILES;
    /**
     * Load configuration from a specific file
     */
    loadFromFile(configPath: string): Promise<EnvGuardConfig>;
    /**
     * Load default configuration by searching for config files
     */
    loadDefault(): Promise<EnvGuardConfig>;
    /**
     * Get default configuration
     */
    private getDefaultConfig;
    /**
     * Validate and normalize configuration
     */
    private validateAndNormalizeConfig;
    /**
     * Validate custom rules
     */
    private validateCustomRules;
    /**
     * Validate security rules
     */
    private validateSecurityRules;
    /**
     * Get file extension
     */
    private getFileExtension;
}
//# sourceMappingURL=loader.d.ts.map