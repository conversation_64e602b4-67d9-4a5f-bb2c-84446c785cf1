#!/usr/bin/env node
/**
 * CLI interface for EnvGuard
 */
export declare class EnvGuardCLI {
    private program;
    constructor();
    /**
     * Setup CLI commands and options
     */
    private setupCommands;
    /**
     * <PERSON>le validate command
     */
    private handleValidateCommand;
    /**
     * Handle init command
     */
    private handleInitCommand;
    /**
     * Handle check command
     */
    private handleCheckCommand;
    /**
     * Display awesome ASCII art banner
     */
    private displayBanner;
    /**
     * Parse CLI options
     */
    private parseOptions;
    /**
     * Load configuration
     */
    private loadConfig;
    /**
     * Run the CLI
     */
    run(argv?: string[]): Promise<void>;
}
//# sourceMappingURL=index.d.ts.map