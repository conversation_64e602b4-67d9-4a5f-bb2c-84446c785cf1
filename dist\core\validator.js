"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvGuardValidator = void 0;
const parser_1 = require("./parser");
const schema_validator_1 = require("./schema-validator");
const example_validator_1 = require("./example-validator");
const analyzer_1 = require("../security/analyzer");
/**
 * Main validator that orchestrates all validation components
 */
class EnvGuardValidator {
    constructor(config) {
        this.config = config;
    }
    /**
     * Validate environment file
     */
    async validate() {
        const startTime = Date.now();
        try {
            // Parse the main .env file
            const envFile = this.config.envFile || '.env';
            const parsedEnv = parser_1.EnvParser.parseFile(envFile);
            if (parsedEnv.parseErrors.length > 0) {
                return this.createErrorResult(parsedEnv.parseErrors, startTime);
            }
            // Determine validation mode
            const mode = this.determineValidationMode();
            // Perform validation based on mode
            let result;
            switch (mode) {
                case 'schema':
                    result = await this.validateWithSchema(parsedEnv.variables);
                    break;
                case 'example':
                    result = await this.validateWithExample(parsedEnv.variables);
                    break;
                case 'both':
                    result = await this.validateWithBoth(parsedEnv.variables);
                    break;
                default:
                    throw new Error(`Unknown validation mode: ${mode}`);
            }
            // Add security analysis
            const securityResult = this.performSecurityAnalysis(parsedEnv.variables);
            result.errors.push(...securityResult.errors);
            result.warnings.push(...securityResult.warnings);
            result.info.push(...securityResult.info);
            // Update summary
            result.summary.securityIssues = securityResult.errors.length;
            result.summary.validationTime = Date.now() - startTime;
            result.isValid = result.errors.length === 0;
            return result;
        }
        catch (error) {
            return this.createErrorResult([{
                    lineNumber: 0,
                    message: `Validation failed: ${error.message}`,
                    line: '',
                }], startTime);
        }
    }
    /**
     * Validate using schema file
     */
    async validateWithSchema(variables) {
        if (!this.config.schemaFile) {
            throw new Error('Schema file is required for schema validation mode');
        }
        const schema = await this.loadSchema(this.config.schemaFile);
        const validator = new schema_validator_1.SchemaValidator(schema);
        return validator.validate(variables);
    }
    /**
     * Validate using example file
     */
    async validateWithExample(variables) {
        if (!this.config.exampleFile) {
            throw new Error('Example file is required for example validation mode');
        }
        const exampleParsed = parser_1.EnvParser.parseFile(this.config.exampleFile);
        if (exampleParsed.parseErrors.length > 0) {
            throw new Error(`Failed to parse example file: ${exampleParsed.parseErrors[0]?.message}`);
        }
        const validator = new example_validator_1.ExampleValidator(exampleParsed.variables, this.config.ignorePatterns || []);
        return validator.validate(variables);
    }
    /**
     * Validate using both schema and example
     */
    async validateWithBoth(variables) {
        const schemaResult = await this.validateWithSchema(variables);
        const exampleResult = await this.validateWithExample(variables);
        // Merge results
        const mergedResult = {
            isValid: schemaResult.isValid && exampleResult.isValid,
            errors: [...schemaResult.errors, ...exampleResult.errors],
            warnings: [...schemaResult.warnings, ...exampleResult.warnings],
            info: [...schemaResult.info, ...exampleResult.info],
            summary: this.mergeSummaries(schemaResult.summary, exampleResult.summary),
        };
        // Remove duplicate issues
        mergedResult.errors = this.deduplicateErrors(mergedResult.errors);
        mergedResult.warnings = this.deduplicateWarnings(mergedResult.warnings);
        return mergedResult;
    }
    /**
     * Perform security analysis
     */
    performSecurityAnalysis(variables) {
        const analyzer = new analyzer_1.SecurityAnalyzer(this.config.securityRules || []);
        return analyzer.analyze(variables);
    }
    /**
     * Determine validation mode based on config
     */
    determineValidationMode() {
        if (this.config.schemaFile && this.config.exampleFile) {
            return 'both';
        }
        else if (this.config.schemaFile) {
            return 'schema';
        }
        else if (this.config.exampleFile) {
            return 'example';
        }
        else {
            // Default to example mode with .env.example
            this.config.exampleFile = '.env.example';
            return 'example';
        }
    }
    /**
     * Load schema from file
     */
    async loadSchema(schemaFile) {
        try {
            const fs = await Promise.resolve().then(() => __importStar(require('fs')));
            const path = await Promise.resolve().then(() => __importStar(require('path')));
            const yaml = await Promise.resolve().then(() => __importStar(require('yaml')));
            const content = fs.readFileSync(schemaFile, 'utf-8');
            const ext = path.extname(schemaFile).toLowerCase();
            if (ext === '.json') {
                return JSON.parse(content);
            }
            else if (ext === '.yaml' || ext === '.yml') {
                return yaml.parse(content);
            }
            else {
                throw new Error(`Unsupported schema file format: ${ext}`);
            }
        }
        catch (error) {
            throw new Error(`Failed to load schema file: ${error.message}`);
        }
    }
    /**
     * Create error result from parse errors
     */
    createErrorResult(parseErrors, startTime) {
        const errors = parseErrors.map(error => ({
            type: 'invalid_format',
            variable: '',
            message: error.message,
            lineNumber: error.lineNumber,
            severity: 'error',
        }));
        return {
            isValid: false,
            errors,
            warnings: [],
            info: [],
            summary: {
                totalVariables: 0,
                requiredVariables: 0,
                missingVariables: 0,
                unusedVariables: 0,
                securityIssues: 0,
                validationTime: Date.now() - startTime,
            },
        };
    }
    /**
     * Merge validation summaries
     */
    mergeSummaries(summary1, summary2) {
        return {
            totalVariables: Math.max(summary1.totalVariables, summary2.totalVariables),
            requiredVariables: Math.max(summary1.requiredVariables, summary2.requiredVariables),
            missingVariables: Math.max(summary1.missingVariables, summary2.missingVariables),
            unusedVariables: Math.max(summary1.unusedVariables, summary2.unusedVariables),
            securityIssues: summary1.securityIssues + summary2.securityIssues,
            validationTime: Math.max(summary1.validationTime, summary2.validationTime),
        };
    }
    /**
     * Remove duplicate errors
     */
    deduplicateErrors(errors) {
        const seen = new Set();
        return errors.filter(error => {
            const key = `${error.type}-${error.variable}-${error.message}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
    /**
     * Remove duplicate warnings
     */
    deduplicateWarnings(warnings) {
        const seen = new Set();
        return warnings.filter(warning => {
            const key = `${warning.type}-${warning.variable}-${warning.message}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
}
exports.EnvGuardValidator = EnvGuardValidator;
//# sourceMappingURL=validator.js.map