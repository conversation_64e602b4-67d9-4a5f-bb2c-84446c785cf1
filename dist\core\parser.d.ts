import { EnvVariable, ParsedEnvFile } from '../types';
/**
 * Parser for .env files with comprehensive error handling and metadata extraction
 */
export declare class EnvParser {
    /**
     * Parse a .env file from file path
     */
    static parseFile(filePath: string): ParsedEnvFile;
    /**
     * Parse .env content from string
     */
    static parseContent(content: string): ParsedEnvFile;
    /**
     * Parse a single variable line
     */
    private static parseVariableLine;
    /**
     * Extract value and inline comment from the right side of assignment
     */
    private static extractValueAndComment;
    /**
     * Process quotes and extract clean value
     */
    private static processQuotes;
    /**
     * Convert parsed variables back to .env format
     */
    static stringify(variables: EnvVariable[]): string;
}
//# sourceMappingURL=parser.d.ts.map